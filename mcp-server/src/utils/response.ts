/**
 * Response utility functions for MCP tools
 * 
 * This module provides standardized response formatting for MCP tools,
 * ensuring consistent JSON output format across all tool responses.
 */

/**
 * Creates a standardized success response for MCP tools
 *
 * @param data - The data to include in the response
 * @returns Formatted MCP tool response
 */
export function createSuccessResponse(data: any) {
  return {
    content: [
      {
        type: "text" as const,
        text: JSON.stringify(data, null, 2),
      } as any,
    ],
  };
}

/**
 * Creates a standardized error response for MCP tools
 * 
 * @param error - Error message
 * @param details - Optional additional error details
 * @returns Formatted MCP tool error response
 */
export function createErrorResponse(error: string, details?: any) {
  const errorData = {
    success: false,
    error,
    ...(details && { details }),
  };

  return {
    content: [
      {
        type: "text" as const,
        text: JSON.stringify(errorData, null, 2),
      } as any,
    ],
    isError: true,
  };
}

/**
 * Creates a response with both text and image content
 * 
 * @param textData - Text data to include
 * @param imageData - Base64 encoded image data
 * @param mimeType - MIME type of the image
 * @returns Formatted MCP tool response with text and image
 */
export function createImageResponse(
  textData: any,
  imageData: string,
  mimeType: string
) {
  return {
    content: [
      {
        type: "text" as const,
        text: JSON.stringify(textData, null, 2),
      } as any,
      {
        type: "image" as const,
        data: imageData,
        mimeType: mimeType,
      } as any,
    ],
  };
}

/**
 * Creates a resource response for MCP resources
 * 
 * @param uri - Resource URI
 * @param data - Data to include in the resource
 * @param mimeType - MIME type (defaults to application/json)
 * @returns Formatted resource response
 */
export function createResourceResponse(
  uri: string,
  data: any,
  mimeType: string = "application/json"
) {
  return {
    contents: [
      {
        uri,
        text: JSON.stringify(data, null, 2),
        mimeType,
      },
    ],
  };
}

/**
 * Creates an error resource response
 * 
 * @param uri - Resource URI
 * @param error - Error message
 * @param mimeType - MIME type (defaults to application/json)
 * @returns Formatted error resource response
 */
export function createResourceErrorResponse(
  uri: string,
  error: string,
  mimeType: string = "application/json"
) {
  return {
    contents: [
      {
        uri,
        text: JSON.stringify({ error }, null, 2),
        mimeType,
      },
    ],
  };
}
