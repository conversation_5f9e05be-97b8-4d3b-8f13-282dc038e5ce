/* API Request Types */
export interface SearchRequest {
  topic: string;
  limit?: number;
}

export interface ProductDetailsRequest {
  asin: string;
}

export interface FetchImageRequest {
  image_url: string;
}

/* API Response Types */
export interface ApiResponse<T> {
  data: T;
  error?: string;
}

export interface SearchResponse {
  search_term: string;
  product_identifiers: { asin: string }[];
}

export interface ProductDetails {
  asin: string;
  title: string;
  description?: string;
  content: string;
  price: number;
  currency: string;
  main_image_url: string;
  secondary_image_urls: string[];
}

/* MCP Tool Input Types */
export interface SearchToolInput {
  topic: string;
  limit?: number;
}

export interface ProductDetailsToolInput {
  asin: string;
}

export interface FetchImageToolInput {
  asin: string;
  imageType?: 'main' | 'thumbnail';
}

/* MCP Response Types */
export interface ProductSearchResult {
  success: boolean;
  searchTerm: string;
  totalFound: number;
  products: Array<{
    asin: string;
    position: number;
  }>;
  message?: string;
}

export interface ProductDetailsResult {
  success: boolean;
  asin: string;
  product?: {
    title: string;
    price: {
      amount: number | null;
      currency: string;
      formatted: string;
    };
    rating: {
      value: string;
      formatted: string;
    };
    reviews: {
      count: string;
      formatted: string;
    };
    images: {
      main: string;
    };
    features: string[];
    url: string;
  };
  error?: string;
}

export interface ImageFetchResult {
  success: boolean;
  asin: string;
  imageType: string;
  message?: string;
  error?: string;
}

/* MCP Content Types */
export interface McpTextContent {
  type: 'text';
  text: string;
}

export interface McpImageContent {
  type: 'image';
  data: string;
  mimeType: string;
}

export type McpContent = McpTextContent | McpImageContent;

export interface McpToolResponse {
  content: McpContent[];
  isError?: boolean;
}

/* Database Types */
export interface SearchHistoryItem {
  id: string;
  topic: string;
  limit: number;
  productCount: number;
  createdAt: Date;
  products: string[];
}

export interface ProductDatabaseItem {
  asin: string;
  title: string | null;
  price: number | null;
  currency: string | null;
  mainImageUrl: string | null;
  description: string | null;
  createdAt: Date;
  updatedAt: Date;
}

/* Service Configuration Types */
export interface ProxyConfig {
  host: string;
  port: number;
  auth?: {
    username: string;
    password: string;
  };
}

export interface AxiosConfig {
  headers: {
    'User-Agent': string;
  };
  timeout: number;
  proxy?: ProxyConfig;
  responseType?: 'arraybuffer' | 'json' | 'text';
}