#!/usr/bin/env node

/**
 * MCP Server for Amazon Product Scraping
 *
 * This server exposes Amazon product scraping functionality through the Model Context Protocol.
 * It provides tools for searching products, fetching product details, and retrieving images.
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import prisma from "./lib/prisma.js";
import logger from "./services/logger.js";

// Import tool handlers
import { searchProductsToolConfig } from "./handlers/search-products.js";
import { productDetailsToolConfig } from "./handlers/product-details.js";
import { fetchImageToolConfig } from "./handlers/fetch-image.js";

// Import resource handlers
import { searchHistoryResourceConfig } from "./handlers/search-history.js";
import { productDatabaseResourceConfig } from "./handlers/product-database.js";

// Create MCP server instance
const server = new McpServer({
  name: "amazon-scraper-mcp",
  version: "1.0.0",
});

// Register tools
server.registerTool(
  searchProductsToolConfig.name,
  searchProductsToolConfig.description,
  searchProductsToolConfig.handler
);

server.registerTool(
  productDetailsToolConfig.name,
  productDetailsToolConfig.description,
  productDetailsToolConfig.handler
);

server.registerTool(
  fetchImageToolConfig.name,
  fetchImageToolConfig.description,
  fetchImageToolConfig.handler
);

// Register resources
server.registerResource(
  searchHistoryResourceConfig.name,
  searchHistoryResourceConfig.uri,
  searchHistoryResourceConfig.description,
  searchHistoryResourceConfig.handler
);

server.registerResource(
  productDatabaseResourceConfig.name,
  productDatabaseResourceConfig.uri,
  productDatabaseResourceConfig.description,
  productDatabaseResourceConfig.handler
);

/**
 * Main function to start the MCP server
 */
async function main() {
  try {
    // Connect to stdio transport for MCP communication
    const transport = new StdioServerTransport();
    await server.connect(transport);

    logger.info("Amazon Scraper MCP Server started successfully");
    // Removed console.error to avoid interfering with MCP stdio communication
  } catch (error) {
    logger.error("Failed to start MCP server:", error as Error);
    // Removed console.error to avoid interfering with MCP stdio communication
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on("SIGINT", async () => {
  // Removed console.error to avoid interfering with MCP stdio communication
  await prisma.$disconnect();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  // Removed console.error to avoid interfering with MCP stdio communication
  await prisma.$disconnect();
  process.exit(0);
});

// Start the server
if (require.main === module) {
  main().catch((_error) => {
    // Removed console.error to avoid interfering with MCP stdio communication
    process.exit(1);
  });
}

export default server;
