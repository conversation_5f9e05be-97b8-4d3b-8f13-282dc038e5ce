/**
 * Amazon Scraping Service
 * 
 * This service handles all Amazon scraping operations including product search,
 * product details extraction, and image fetching.
 */

import axios from 'axios';
import * as cheerio from 'cheerio';
import { AxiosConfig } from '../types/index';
import userAgentService from './user-agent';
import proxyService from './proxy.js';
import logger from './logger.js';

// Amazon base URL
const AMAZON_BASE_URL = "https://www.amazon.sg";

export interface ProductSearchOptions {
  topic: string;
  limit?: number;
}

export interface ProductSearchResult {
  products: Array<{ asin: string }>;
  totalFound: number;
}

export interface ProductDetails {
  title: string;
  price: number | null;
  currency: string;
  rating: string;
  reviewCount: string;
  mainImage: string;
  features: string[];
  url: string;
}

export interface ImageFetchResult {
  imageData: string;
  mimeType: string;
}

/**
 * Amazon Scraping Service Class
 */
class AmazonScraperService {
  /**
   * Creates axios configuration with user agent and proxy settings
   */
  private async createAxiosConfig(responseType?: 'arraybuffer' | 'json' | 'text'): Promise<AxiosConfig> {
    const userAgent = userAgentService.getRotatedUserAgent();
    const proxyConfig = proxyService.isEnabled()
      ? await proxyService.getProxyConfig()
      : null;

    const config: AxiosConfig = {
      headers: {
        "User-Agent": userAgent,
      },
      timeout: 30000,
    };

    if (proxyConfig) {
      config.proxy = proxyConfig;
    }

    if (responseType) {
      config.responseType = responseType;
    }

    return config;
  }

  /**
   * Search for products on Amazon
   */
  async searchProducts(options: ProductSearchOptions): Promise<ProductSearchResult> {
    const { topic, limit = 10 } = options;
    const searchUrl = `${AMAZON_BASE_URL}/s?k=${encodeURIComponent(topic)}`;

    logger.logScrapingAttempt({
      topic,
      url: searchUrl,
    });

    try {
      const axiosConfig = await this.createAxiosConfig();
      const response = await axios.get<string>(searchUrl, axiosConfig);

      logger.logScrapingSuccess({
        topic,
        url: searchUrl,
        statusCode: response.status,
        responseTime: Date.now(),
        userAgent: axiosConfig.headers['User-Agent'],
        proxyUrl: axiosConfig.proxy?.host,
      });

      // Parse HTML with Cheerio
      const $ = cheerio.load(response.data);

      // Extract product ASINs from the page
      const products: { asin: string }[] = [];

      // Look for elements with data-asin attribute (Amazon's product containers)
      $("[role=listitem][data-asin]").each((_index, element) => {
        const asin = $(element).attr("data-asin");

        // Only add valid ASINs and respect the limit
        if (asin && asin.trim() !== "" && products.length < limit) {
          products.push({ asin });
        }
      });

      return {
        products,
        totalFound: products.length,
      };
    } catch (error: any) {
      logger.logScrapingFailure({
        topic,
        url: searchUrl,
        error: error.message,
        responseTime: Date.now(),
        statusCode: error.response?.status,
      });

      throw new Error(`Failed to search products: ${error.message}`);
    }
  }

  /**
   * Get detailed information about a specific product
   */
  async getProductDetails(asin: string): Promise<ProductDetails> {
    const productUrl = `${AMAZON_BASE_URL}/dp/${asin}`;

    logger.logScrapingAttempt({
      topic: `Product details for ${asin}`,
      url: productUrl,
    });

    try {
      const axiosConfig = await this.createAxiosConfig();
      const response = await axios.get<string>(productUrl, axiosConfig);

      logger.logScrapingSuccess({
        topic: `Product details for ${asin}`,
        url: productUrl,
        statusCode: response.status,
        responseTime: Date.now(),
        userAgent: axiosConfig.headers['User-Agent'],
        proxyUrl: axiosConfig.proxy?.host,
      });

      // Parse HTML with Cheerio
      const $ = cheerio.load(response.data);

      // Extract product details
      const title = $("#productTitle").text().trim();
      const priceWhole = $(".a-price-whole").first().text().trim();
      const priceFraction = $(".a-price-fraction").first().text().trim();
      const priceString = priceWhole + priceFraction;
      const price = priceString
        ? parseFloat(priceString.replace(/[^\d.]/g, ""))
        : null;
      const currency = $(".a-price-symbol").first().text().trim();
      const rating =
        $(".a-icon-alt")
          .first()
          .text()
          .match(/[\d.]+/)?.[0] || "";
      const reviewCount =
        $(".a-size-base")
          .filter((_, el) => $(el).text().includes("ratings"))
          .text()
          .match(/[\d,]+/)?.[0] || "";

      // Extract main product image
      const mainImage =
        $("#landingImage").attr("src") ||
        $(".a-dynamic-image").first().attr("src") ||
        "";

      // Extract product features/bullets
      const features: string[] = [];
      $("#feature-bullets ul li span").each((_, element) => {
        const feature = $(element).text().trim();
        if (feature && !feature.includes("Make sure") && feature.length > 10) {
          features.push(feature);
        }
      });

      return {
        title: title || "Not found",
        price,
        currency: currency || "",
        rating: rating || "",
        reviewCount: reviewCount || "",
        mainImage: mainImage || "",
        features,
        url: productUrl,
      };
    } catch (error: any) {
      logger.logScrapingFailure({
        topic: `Product details for ${asin}`,
        url: productUrl,
        error: error.message,
        responseTime: Date.now(),
        statusCode: error.response?.status,
      });

      throw new Error(`Failed to get product details: ${error.message}`);
    }
  }

  /**
   * Fetch product image data
   */
  async fetchProductImage(imageUrl: string): Promise<ImageFetchResult> {
    try {
      const axiosConfig = await this.createAxiosConfig('arraybuffer');
      const response = await axios.get(imageUrl, axiosConfig);
      
      const imageData = Buffer.from(response.data as ArrayBuffer).toString('base64');
      const mimeType = response.headers["content-type"] || "image/jpeg";

      return {
        imageData,
        mimeType,
      };
    } catch (error: any) {
      throw new Error(`Failed to fetch image: ${error.message}`);
    }
  }
}

// Export singleton instance
const amazonScraperService = new AmazonScraperService();
export default amazonScraperService;
