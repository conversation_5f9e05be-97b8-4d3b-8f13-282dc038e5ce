/**
 * MCP Tool Handler: Fetch Product Image
 * 
 * This handler implements the fetch-product-image MCP tool that downloads
 * and returns a product image from Amazon.
 */

import { z } from 'zod';
import { ImageFetchResult } from '../types/index';
import { createErrorResponse, createImageResponse } from '../utils/response';
import amazonScraperService from '../services/amazon-scraper';
import prisma from '../lib/prisma';

/**
 * Input schema for the fetch-product-image tool
 */
export const fetchImageSchema = {
  asin: z
    .string()
    .describe("Amazon Standard Identification Number (ASIN) of the product"),
  imageType: z
    .enum(["main", "thumbnail"])
    .default("main")
    .describe("Type of image to fetch"),
};

/**
 * Handler function for the fetch-product-image tool
 *
 * @param args - Tool input parameters
 * @returns MCP tool response with image data
 */
export async function handleFetchImage(args: { asin: string; imageType?: "main" | "thumbnail" }) {
  const { asin, imageType = "main" } = args;

  try {
    // First get the product details to find the image URL
    const product = await prisma.product.findUnique({
      where: { asin },
    });

    if (!product || !product.mainImageUrl) {
      return createErrorResponse(
        `No image URL found for ASIN ${asin}. Try running "get-product-details" first to fetch product information.`,
        {
          asin,
          imageType,
        }
      );
    }

    const imageUrl = product.mainImageUrl;

    // Fetch the image using the Amazon scraper service
    const imageResult = await amazonScraperService.fetchProductImage(imageUrl);

    // Format response according to MCP standards
    const response: ImageFetchResult = {
      success: true,
      asin,
      imageType,
      message: `Successfully fetched ${imageType} image for ASIN ${asin}`,
    };

    return createImageResponse(response, imageResult.imageData, imageResult.mimeType);
  } catch (error: any) {
    return createErrorResponse(`Error fetching image for ASIN ${asin}: ${error.message}`, {
      asin,
      imageType,
    });
  }
}

/**
 * Tool configuration for registration with MCP server
 */
export const fetchImageToolConfig = {
  name: "fetch-product-image",
  description: {
    title: "Fetch Product Image",
    description: "Download and return a product image from Amazon",
    inputSchema: fetchImageSchema,
  },
  handler: handleFetchImage,
};
