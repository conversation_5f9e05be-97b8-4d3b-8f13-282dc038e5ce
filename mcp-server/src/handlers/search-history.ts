/**
 * MCP Resource Handler: Search History
 * 
 * This handler implements the search-history MCP resource that provides
 * access to recent search queries.
 */

import { SearchHistoryItem } from '../types/index.js';
import { createResourceResponse, createResourceErrorResponse } from '../utils/response.js';
import prisma from '../lib/prisma.js';

/**
 * Handler function for the search-history resource
 * 
 * @param uri - Resource URI
 * @returns Resource response with search history
 */
export async function handleSearchHistory(uri: URL) {
  try {
    const recentSearches = await prisma.searchQuery.findMany({
      take: 20,
      orderBy: { createdAt: "desc" },
      include: {
        products: {
          select: { asin: true },
        },
      },
    });

    const searchHistory = {
      success: true,
      totalSearches: recentSearches.length,
      searches: recentSearches.map((search): SearchHistoryItem => ({
        id: search.id,
        topic: search.topic,
        limit: search.limit,
        productCount: search.products.length,
        createdAt: search.createdAt,
        products: search.products.map((p) => p.asin),
      })),
    };

    return createResourceResponse(uri.href, searchHistory);
  } catch (error: any) {
    return createResourceErrorResponse(uri.href, error.message);
  }
}

/**
 * Resource configuration for registration with MCP server
 */
export const searchHistoryResourceConfig = {
  name: "search-history",
  uri: "search://history",
  description: {
    title: "Search History",
    description: "Recent Amazon product search queries",
    mimeType: "application/json",
  },
  handler: handleSearchHistory,
};
