/**
 * MCP Tool Handler: Get Product Details
 * 
 * This handler implements the get-product-details MCP tool that fetches
 * detailed information about a specific Amazon product using its ASIN.
 */

import { z } from 'zod';
import { ProductDetailsResult } from '../types/index.js';
import { createSuccessResponse, createErrorResponse } from '../utils/response.js';
import amazonScraperService from '../services/amazon-scraper.js';
import prisma from '../lib/prisma.js';

/**
 * Input schema for the get-product-details tool
 */
export const productDetailsSchema = {
  asin: z
    .string()
    .describe("Amazon Standard Identification Number (ASIN) of the product"),
};

/**
 * Handler function for the get-product-details tool
 *
 * @param args - Tool input parameters
 * @returns MCP tool response with product details
 */
export async function handleProductDetails(args: { asin: string }) {
  const { asin } = args;

  try {
    // Get product details using the Amazon scraper service
    const productDetails = await amazonScraperService.getProductDetails(asin);

    // Save/update product details in database
    await prisma.product.upsert({
      where: { asin },
      update: {
        title: productDetails.title || undefined,
        price: productDetails.price || undefined,
        currency: productDetails.currency || undefined,
        mainImageUrl: productDetails.mainImage || undefined,
        description: productDetails.features.length > 0 ? productDetails.features.join("\n") : undefined,
      },
      create: {
        asin,
        title: productDetails.title || undefined,
        price: productDetails.price || undefined,
        currency: productDetails.currency || undefined,
        mainImageUrl: productDetails.mainImage || undefined,
        description: productDetails.features.length > 0 ? productDetails.features.join("\n") : undefined,
      },
    });

    // Format response according to MCP standards
    const response: ProductDetailsResult = {
      success: true,
      asin,
      product: {
        title: productDetails.title,
        price: {
          amount: productDetails.price,
          currency: productDetails.currency,
          formatted: productDetails.price 
            ? `${productDetails.currency} ${productDetails.price}` 
            : "Not available",
        },
        rating: {
          value: productDetails.rating,
          formatted: productDetails.rating ? `${productDetails.rating}/5` : "Not available",
        },
        reviews: {
          count: productDetails.reviewCount,
          formatted: productDetails.reviewCount || "Not available",
        },
        images: {
          main: productDetails.mainImage,
        },
        features: productDetails.features,
        url: productDetails.url,
      },
    };

    return createSuccessResponse(response);
  } catch (error: any) {
    return createErrorResponse(`Error fetching product details for ASIN ${asin}: ${error.message}`, {
      asin,
    });
  }
}

/**
 * Tool configuration for registration with MCP server
 */
export const productDetailsToolConfig = {
  name: "get-product-details",
  description: {
    title: "Get Amazon Product Details",
    description: "Fetch detailed information about an Amazon product using its ASIN",
    inputSchema: productDetailsSchema,
  },
  handler: handleProductDetails,
};
